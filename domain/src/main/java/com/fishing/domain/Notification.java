package com.fishing.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知实体
 */
@TableName(value = "notifications")
@Data
public class Notification implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 接收用户ID
     */
    private Long userId;

    /**
     * 通知类型：like-点赞, comment-评论, follow-关注, system-系统
     */
    private String type;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 是否已读：0-未读, 1-已读
     */
    private Boolean isRead;

    /**
     * 关联类型：moment-动态, user-用户, system-系统
     */
    private String relatedType;

    /**
     * 关联ID
     */
    private Long relatedId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 通知类型枚举
     */
    public static class Type {
        public static final String LIKE = "like";
        public static final String COMMENT = "comment";
        public static final String FOLLOW = "follow";
        public static final String SYSTEM = "system";
    }

    /**
     * 关联类型枚举
     */
    public static class RelatedType {
        public static final String MOMENT = "moment";
        public static final String USER = "user";
        public static final String SYSTEM = "system";
    }
}
