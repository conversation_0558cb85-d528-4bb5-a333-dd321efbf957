# 社区功能开发文档

## 概述

本文档描述了钓鱼应用社区功能的实现，包括收藏、举报、通知等核心功能。

## 新增功能

### 1. 收藏功能

#### 功能描述
- 用户可以收藏动态和钓点
- 支持取消收藏
- 查看收藏状态

#### API 接口
- `POST /bookmarks/moment/{momentId}` - 收藏动态
- `DELETE /bookmarks/moment/{momentId}` - 取消收藏动态
- `GET /bookmarks/moment/{momentId}/status` - 查看收藏状态

#### 数据库表
- `bookmarks` - 收藏记录表

### 2. 举报功能

#### 功能描述
- 用户可以举报不当动态和用户
- 管理员可以处理举报
- 防止重复举报

#### API 接口
- `POST /reports/moment` - 举报动态
- `POST /reports/user` - 举报用户
- `GET /reports/reasons` - 获取举报原因列表

#### 数据库表
- `reports` - 举报记录表

### 3. 通知功能

#### 功能描述
- 系统通知推送
- 点赞、评论、关注通知
- 标记已读/未读
- 通知数量统计

#### API 接口
- `GET /notifications` - 获取通知列表
- `PUT /notifications/{id}/read` - 标记已读
- `PUT /notifications/read-all` - 全部标记已读
- `GET /notifications/unread-count` - 未读数量
- `DELETE /notifications/{id}` - 删除通知

#### 数据库表
- `notifications` - 通知记录表

## 数据库设计

### 收藏表 (bookmarks)
```sql
CREATE TABLE `bookmarks` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `target_type` varchar(20) NOT NULL,
  `target_id` bigint NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`)
);
```

### 举报表 (reports)
```sql
CREATE TABLE `reports` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `reporter_id` bigint NOT NULL,
  `target_type` varchar(20) NOT NULL,
  `target_id` bigint NOT NULL,
  `reason` varchar(50) NOT NULL,
  `description` text,
  `status` tinyint NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 通知表 (notifications)
```sql
CREATE TABLE `notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `type` varchar(20) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` varchar(500) NOT NULL,
  `is_read` tinyint NOT NULL DEFAULT 0,
  `related_type` varchar(20),
  `related_id` bigint,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## 部署说明

### 1. 数据库初始化
```bash
# 执行数据库初始化脚本
mysql -u username -p database_name < sql/init_community_features.sql
```

### 2. 应用配置
无需额外配置，使用现有的数据库和缓存配置。

### 3. 依赖检查
确保以下服务正常运行：
- MySQL 数据库
- Redis 缓存（如果使用）
- 用户认证服务

## 前端集成

### 1. API 调用示例
```dart
// 收藏动态
await bookmarkApi.bookmarkMoment(momentId);

// 举报动态
await reportApi.reportMoment(momentId, reason, description: description);

// 获取通知
final notifications = await notificationApi.getNotifications();
```

### 2. 状态管理
使用 CommunityViewModel 管理社区相关状态：
- 收藏状态
- 举报状态
- 通知状态

## 性能优化

### 1. 数据库优化
- 添加了必要的索引
- 使用批量查询减少数据库访问
- 创建统计视图提高查询效率

### 2. 缓存策略
- 收藏状态可以缓存
- 通知数量可以缓存
- 举报原因列表可以缓存

### 3. 分页查询
所有列表查询都支持分页，避免大量数据传输。

## 安全考虑

### 1. 权限控制
- 用户只能操作自己的收藏和通知
- 举报需要登录用户
- 管理员才能处理举报

### 2. 防刷机制
- 防止重复举报
- 防止频繁收藏/取消收藏
- 通知创建需要权限验证

### 3. 数据验证
- 输入参数验证
- SQL 注入防护
- XSS 攻击防护

## 监控和日志

### 1. 关键指标
- 收藏数量统计
- 举报处理时效
- 通知推送成功率

### 2. 日志记录
- 用户操作日志
- 错误日志
- 性能日志

## 后续扩展

### 1. 功能扩展
- 收藏夹分类
- 举报处理工作流
- 实时通知推送

### 2. 性能扩展
- 读写分离
- 分库分表
- 消息队列

## 测试建议

### 1. 单元测试
- Service 层业务逻辑测试
- Mapper 层数据访问测试

### 2. 集成测试
- API 接口测试
- 数据库事务测试

### 3. 性能测试
- 并发收藏测试
- 大量通知推送测试
