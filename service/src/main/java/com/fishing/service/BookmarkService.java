package com.fishing.service;

import java.util.List;

/**
 * 收藏服务接口
 */
public interface BookmarkService {

    /**
     * 收藏动态
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     */
    void bookmarkMoment(Long userId, Long momentId);

    /**
     * 取消收藏动态
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     */
    void unbookmarkMoment(Long userId, Long momentId);

    /**
     * 检查动态是否已收藏
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     * @return 是否已收藏
     */
    boolean isBookmarked(Long userId, Long momentId);

    /**
     * 批量检查动态是否已收藏
     *
     * @param userId    用户ID
     * @param momentIds 动态ID列表
     * @return 已收藏的动态ID列表
     */
    List<Long> batchGetBookmarkStatus(Long userId, List<Long> momentIds);
}
