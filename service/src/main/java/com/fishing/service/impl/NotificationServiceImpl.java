package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.Notification;
import com.fishing.mapper.NotificationMapper;
import com.fishing.service.NotificationService;
import com.fishing.vo.notification.NotificationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 通知服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

    private final NotificationMapper notificationMapper;

    @Override
    public List<NotificationVO> getUserNotifications(Long userId, Integer page, Integer size, Boolean unreadOnly) {
        // 1. 构建分页参数
        Page<NotificationVO> pageParam = new Page<>(page, size);

        // 2. 分页查询通知
        Page<NotificationVO> result = notificationMapper.selectUserNotifications(pageParam, userId, unreadOnly);

        log.info("Getting notifications for user {}, page: {}, size: {}, unreadOnly: {}, total: {}",
                userId, page, size, unreadOnly, result.getTotal());

        return result.getRecords();
    }

    @Override
    @Transactional
    public void markAsRead(Long userId, Long notificationId) {
        // 1. 检查通知是否属于该用户
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Notification::getId, notificationId)
                   .eq(Notification::getUserId, userId);

        Notification notification = notificationMapper.selectOne(queryWrapper);
        if (notification == null) {
            throw new IllegalArgumentException("通知不存在或不属于该用户");
        }

        // 2. 更新通知状态为已读
        if (!notification.getIsRead()) {
            LambdaUpdateWrapper<Notification> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Notification::getId, notificationId)
                        .set(Notification::getIsRead, true);

            notificationMapper.update(null, updateWrapper);
            log.info("User {} marked notification {} as read", userId, notificationId);
        }
    }

    @Override
    @Transactional
    public void markAllAsRead(Long userId) {
        // 更新该用户所有未读通知为已读
        int updated = notificationMapper.markAllAsRead(userId);
        log.info("User {} marked {} notifications as read", userId, updated);
    }

    @Override
    public int getUnreadCount(Long userId) {
        int count = notificationMapper.getUnreadCount(userId);
        log.debug("Getting unread count for user {}: {}", userId, count);
        return count;
    }

    @Override
    @Transactional
    public void deleteNotification(Long userId, Long notificationId) {
        // 1. 检查通知是否属于该用户
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Notification::getId, notificationId)
                   .eq(Notification::getUserId, userId);

        Notification notification = notificationMapper.selectOne(queryWrapper);
        if (notification == null) {
            throw new IllegalArgumentException("通知不存在或不属于该用户");
        }

        // 2. 删除通知记录
        notificationMapper.deleteById(notificationId);
        log.info("User {} deleted notification {}", userId, notificationId);
    }

    @Override
    @Transactional
    public void createNotification(Long userId, String type, String title, String content,
                                 String relatedType, Long relatedId, String imageUrl) {
        // 1. 创建通知记录
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setType(type);
        notification.setTitle(title);
        notification.setContent(content);
        notification.setIsRead(false);
        notification.setRelatedType(relatedType);
        notification.setRelatedId(relatedId);
        notification.setImageUrl(imageUrl);

        notificationMapper.insert(notification);

        // 2. TODO: 可选推送实时通知
        // webSocketService.sendNotification(userId, notification);

        log.info("Created notification for user {}: type={}, title={}", userId, type, title);
    }
}
