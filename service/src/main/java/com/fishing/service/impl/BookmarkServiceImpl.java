package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fishing.domain.Bookmark;
import com.fishing.domain.moment.Moment;
import com.fishing.mapper.BookmarkMapper;
import com.fishing.mapper.MomentMapper;
import com.fishing.service.BookmarkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 收藏服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BookmarkServiceImpl implements BookmarkService {

    private final BookmarkMapper bookmarkMapper;
    private final MomentMapper momentMapper;

    @Override
    @Transactional
    public void bookmarkMoment(Long userId, Long momentId) {
        // 1. 检查动态是否存在
        Moment moment = momentMapper.selectById(momentId);
        if (moment == null) {
            throw new IllegalArgumentException("动态不存在");
        }

        // 2. 检查是否已收藏
        boolean isBookmarked = bookmarkMapper.isBookmarked(userId, Bookmark.TargetType.MOMENT, momentId);
        if (isBookmarked) {
            log.warn("User {} already bookmarked moment {}", userId, momentId);
            return;
        }

        // 3. 创建收藏记录
        Bookmark bookmark = new Bookmark();
        bookmark.setUserId(userId);
        bookmark.setTargetType(Bookmark.TargetType.MOMENT);
        bookmark.setTargetId(momentId);

        bookmarkMapper.insert(bookmark);
        log.info("User {} bookmarked moment {}", userId, momentId);
    }

    @Override
    @Transactional
    public void unbookmarkMoment(Long userId, Long momentId) {
        // 1. 检查收藏记录是否存在
        boolean isBookmarked = bookmarkMapper.isBookmarked(userId, Bookmark.TargetType.MOMENT, momentId);
        if (!isBookmarked) {
            log.warn("User {} has not bookmarked moment {}", userId, momentId);
            return;
        }

        // 2. 删除收藏记录
        int deleted = bookmarkMapper.deleteBookmark(userId, Bookmark.TargetType.MOMENT, momentId);
        if (deleted > 0) {
            log.info("User {} unbookmarked moment {}", userId, momentId);
        }
    }

    @Override
    public boolean isBookmarked(Long userId, Long momentId) {
        boolean bookmarked = bookmarkMapper.isBookmarked(userId, Bookmark.TargetType.MOMENT, momentId);
        log.debug("Checking if user {} bookmarked moment {}: {}", userId, momentId, bookmarked);
        return bookmarked;
    }

    @Override
    public List<Long> batchGetBookmarkStatus(Long userId, List<Long> momentIds) {
        if (momentIds == null || momentIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> bookmarkedIds = bookmarkMapper.batchGetBookmarkStatus(userId, Bookmark.TargetType.MOMENT, momentIds);
        log.debug("Batch checking bookmark status for user {}: {} out of {} moments bookmarked",
                userId, bookmarkedIds.size(), momentIds.size());
        return bookmarkedIds;
    }
}
