package com.fishing.service.impl;

import com.fishing.domain.Report;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.dto.report.ReportMomentDTO;
import com.fishing.dto.report.ReportUserDTO;
import com.fishing.mapper.ReportMapper;
import com.fishing.mapper.MomentMapper;
import com.fishing.mapper.UserMapper;
import com.fishing.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 举报服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private final ReportMapper reportMapper;
    private final MomentMapper momentMapper;
    private final UserMapper userMapper;

    @Override
    @Transactional
    public void reportMoment(Long userId, ReportMomentDTO reportDTO) {
        // 1. 检查动态是否存在
        Moment moment = momentMapper.selectById(reportDTO.getMomentId());
        if (moment == null) {
            throw new IllegalArgumentException("动态不存在");
        }

        // 2. 检查是否重复举报
        boolean hasReported = reportMapper.hasReported(userId, Report.TargetType.MOMENT, reportDTO.getMomentId());
        if (hasReported) {
            throw new IllegalArgumentException("您已经举报过该动态");
        }

        // 3. 创建举报记录
        Report report = new Report();
        report.setReporterId(userId);
        report.setTargetType(Report.TargetType.MOMENT);
        report.setTargetId(reportDTO.getMomentId());
        report.setReason(reportDTO.getReason());
        report.setDescription(reportDTO.getDescription());
        report.setStatus(Report.Status.PENDING);

        reportMapper.insert(report);

        // 4. TODO: 发送通知给管理员
        // notificationService.notifyAdmins("新举报", "有用户举报了动态", ...);

        log.info("User {} reported moment {} for reason: {}",
                userId, reportDTO.getMomentId(), reportDTO.getReason());
    }

    @Override
    @Transactional
    public void reportUser(Long userId, ReportUserDTO reportDTO) {
        // 1. 检查用户是否存在
        User user = userMapper.selectById(reportDTO.getUserId());
        if (user == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 2. 检查是否重复举报
        boolean hasReported = reportMapper.hasReported(userId, Report.TargetType.USER, reportDTO.getUserId());
        if (hasReported) {
            throw new IllegalArgumentException("您已经举报过该用户");
        }

        // 3. 创建举报记录
        Report report = new Report();
        report.setReporterId(userId);
        report.setTargetType(Report.TargetType.USER);
        report.setTargetId(reportDTO.getUserId());
        report.setReason(reportDTO.getReason());
        report.setDescription(reportDTO.getDescription());
        report.setStatus(Report.Status.PENDING);

        reportMapper.insert(report);

        // 4. TODO: 发送通知给管理员
        // notificationService.notifyAdmins("新举报", "有用户举报了用户", ...);

        log.info("User {} reported user {} for reason: {}",
                userId, reportDTO.getUserId(), reportDTO.getReason());
    }
}
