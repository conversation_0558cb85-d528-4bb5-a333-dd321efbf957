package com.fishing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.Notification;
import com.fishing.vo.notification.NotificationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 通知Mapper接口
 */
@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 分页查询用户通知
     *
     * @param page       分页参数
     * @param userId     用户ID
     * @param unreadOnly 是否只查询未读
     * @return 通知列表
     */
    Page<NotificationVO> selectUserNotifications(Page<NotificationVO> page, 
                                               @Param("userId") Long userId, 
                                               @Param("unreadOnly") Boolean unreadOnly);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int getUnreadCount(@Param("userId") Long userId);

    /**
     * 标记所有通知为已读
     *
     * @param userId 用户ID
     * @return 更新的记录数
     */
    int markAllAsRead(@Param("userId") Long userId);
}
