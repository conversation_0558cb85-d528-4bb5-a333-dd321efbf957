package com.fishing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fishing.domain.Bookmark;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收藏Mapper接口
 */
@Mapper
public interface BookmarkMapper extends BaseMapper<Bookmark> {

    /**
     * 检查是否已收藏
     *
     * @param userId     用户ID
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 是否已收藏
     */
    boolean isBookmarked(@Param("userId") Long userId,
                         @Param("targetType") String targetType,
                         @Param("targetId") Long targetId);

    /**
     * 删除收藏记录
     *
     * @param userId     用户ID
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 删除的记录数
     */
    int deleteBookmark(@Param("userId") Long userId,
                       @Param("targetType") String targetType,
                       @Param("targetId") Long targetId);

    /**
     * 批量查询收藏状态
     *
     * @param userId     用户ID
     * @param targetType 目标类型
     * @param targetIds  目标ID列表
     * @return 已收藏的目标ID列表
     */
    List<Long> batchGetBookmarkStatus(@Param("userId") Long userId,
                                      @Param("targetType") String targetType,
                                      @Param("targetIds") List<Long> targetIds);
}
