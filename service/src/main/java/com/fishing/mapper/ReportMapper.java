package com.fishing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fishing.domain.Report;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 举报Mapper接口
 */
@Mapper
public interface ReportMapper extends BaseMapper<Report> {

    /**
     * 检查是否已举报过
     *
     * @param reporterId 举报人ID
     * @param targetType 目标类型
     * @param targetId   目标ID
     * @return 是否已举报过
     */
    boolean hasReported(@Param("reporterId") Long reporterId, 
                       @Param("targetType") String targetType, 
                       @Param("targetId") Long targetId);
}
