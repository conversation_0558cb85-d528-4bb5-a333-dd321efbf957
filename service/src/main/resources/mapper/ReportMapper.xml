<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fishing.mapper.ReportMapper">

    <!-- 检查是否已举报过 -->
    <select id="hasReported" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM reports
        WHERE reporter_id = #{reporterId}
          AND target_type = #{targetType}
          AND target_id = #{targetId}
    </select>

</mapper>
