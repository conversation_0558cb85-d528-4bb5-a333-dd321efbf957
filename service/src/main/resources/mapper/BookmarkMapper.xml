<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fishing.mapper.BookmarkMapper">

  <!-- 检查是否已收藏 -->
  <select id="isBookmarked" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM bookmarks
        WHERE user_id = #{userId}
          AND target_type = #{targetType}
          AND target_id = #{targetId}
  </select>

  <!-- 删除收藏记录 -->
  <delete id="deleteBookmark">
        DELETE FROM bookmarks
        WHERE user_id = #{userId}
          AND target_type = #{targetType}
          AND target_id = #{targetId}
  </delete>

  <!-- 批量查询收藏状态 -->
  <select id="batchGetBookmarkStatus" resultType="long">
        SELECT target_id
        FROM bookmarks
        WHERE user_id = #{userId}
          AND target_type = #{targetType}
          AND target_id IN
    <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
            #{targetId}
    </foreach>
  </select>

</mapper>
