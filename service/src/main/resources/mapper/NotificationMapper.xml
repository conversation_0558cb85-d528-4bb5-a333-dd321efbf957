<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fishing.mapper.NotificationMapper">

    <!-- 分页查询用户通知 -->
    <select id="selectUserNotifications" resultType="com.fishing.vo.notification.NotificationVO">
        SELECT 
            id,
            user_id as userId,
            type,
            title,
            content,
            is_read as isRead,
            related_type as relatedType,
            related_id as relatedId,
            image_url as imageUrl,
            created_at as createdAt
        FROM notifications
        WHERE user_id = #{userId}
        <if test="unreadOnly != null and unreadOnly">
            AND is_read = 0
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 获取用户未读通知数量 -->
    <select id="getUnreadCount" resultType="int">
        SELECT COUNT(1)
        FROM notifications
        WHERE user_id = #{userId}
          AND is_read = 0
    </select>

    <!-- 标记所有通知为已读 -->
    <update id="markAllAsRead">
        UPDATE notifications
        SET is_read = 1,
            updated_at = NOW()
        WHERE user_id = #{userId}
          AND is_read = 0
    </update>

</mapper>
