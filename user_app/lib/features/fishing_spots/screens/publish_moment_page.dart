import 'package:flutter/material.dart';
import 'package:flutter_reorderable_grid_view/widgets/reorderable_builder.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/fishing_spots/constants/publish_moment_constants.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/screens/location_selection_page.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/publish_moment_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/enhanced_fishing_catch_form.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/equipment_form.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/moment_type_selector.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/question_form.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/technique_form.dart';
import 'package:user_app/models/image/uploaded_image.dart';

class PublishMomentPage extends StatefulWidget {
  final String? initialMomentType;

  const PublishMomentPage({super.key, this.initialMomentType});

  @override
  State<PublishMomentPage> createState() => _PublishMomentPageState();
}

class _PublishMomentPageState extends State<PublishMomentPage>
    with SingleTickerProviderStateMixin {
  final TextEditingController _contentController = TextEditingController();
  late final PublishMomentViewModel _viewModel;
  late final AnimationController _animationController;
  late final Animation<double> _animation;

  final FocusNode _contentFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  bool _isPublishing = false;

  void _selectLocation() async {
    final result = await Navigator.push<FishingSpotVo>(
      context,
      MaterialPageRoute(
        builder: (context) => LocationSelectionPage(),
      ),
    );

    // If a spot was selected, update the viewModel
    if (result != null && mounted) {
      _viewModel.setFishingSpot(result);
    }
  }

  @override
  void initState() {
    super.initState();
    _viewModel = getIt<PublishMomentViewModel>();

    // 设置初始动态类型（如果有传递）
    if (widget.initialMomentType != null) {
      _viewModel.setMomentType(widget.initialMomentType!);
    }

    // Preload fish types for potential use
    // This way the data will be ready if/when the user selects "钓获分享"
    final fishingSpotViewModel = getIt<FishingSpotViewModel>();
    fishingSpotViewModel.loadFishTypes(); // Start loading in background

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart,
    );

    _animationController.forward();

    // Listen to content changes to track if publish should be enabled
    _contentController.addListener(_updatePublishState);

    // Listen to focus changes to scroll content into view
    _contentFocusNode.addListener(() {
      if (_contentFocusNode.hasFocus) {
        // Scroll to ensure text field is visible when focused
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
  }

  void _updatePublishState() {
    _viewModel.setContent(_contentController.text);
  }

  Future<void> _pickImages() async {
    final result = await _viewModel.pickImages();

    if (mounted && result['message'] != null && result['message'].isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          behavior: SnackBarBehavior.floating,
          backgroundColor: result['success'] ? null : Colors.red,
        ),
      );
    }
  }

  Future<void> _publish() async {
    setState(() {
      _isPublishing = true;
    });

    final result = await _viewModel.publish();

    if (mounted) {
      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message']),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green,
          ),
        );

        // Slight delay for feedback before closing
        Future.delayed(const Duration(milliseconds: 800), () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
      } else {
        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message']),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isPublishing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    _contentFocusNode.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<PublishMomentViewModel>(
        builder: (context, viewModel, child) {
          final bool blockPop =
              viewModel.content.isNotEmpty || viewModel.images.isNotEmpty;

          return PopScope(
            canPop: !blockPop,
            onPopInvokedWithResult: (bool didPop, dynamic result) async {
              if (didPop) {
                return; // Pop already happened or is proceeding, nothing to intercept.
              }

              final bool? shouldPop = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  title: const Text('放弃编辑？'),
                  actions: [
                    TextButton(
                      // Close the dialog, returning false (don't pop page)
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('取消'),
                    ),
                    FilledButton(
                      // Close the dialog, returning true (request to pop page)
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('确认'),
                    ),
                  ],
                ),
              );

              // If the user confirmed in the dialog (dialog returned true) AND
              // the widget is still mounted...
              if (shouldPop == true && mounted) {
                // ...manually POP the page now.
                // We don't pass any specific result here unless needed for the previous screen.
                Navigator.of(context).pop();
              }
              // If shouldPop is false or null (dialog cancelled), do nothing.
              // The pop was already prevented by canPop: false.
            },
            child: Scaffold(
              appBar: AppBar(
                title: Text(viewModel.momentType.isEmpty
                    ? '发布动态'
                    : '发布${viewModel.momentType}'),
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    if (viewModel.content.isNotEmpty ||
                        viewModel.images.isNotEmpty) {
                      // Show confirmation dialog
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          title: const Text('放弃编辑？'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('取消'),
                            ),
                            FilledButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                                Navigator.of(context).pop();
                              },
                              child: const Text('确认'),
                            ),
                          ],
                        ),
                      );
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                ),
                actions: [
                  if (_isPublishing)
                    Container(
                      padding: const EdgeInsets.all(14),
                      width: 46,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  else
                    TextButton(
                      onPressed: viewModel.canPublish ? _publish : null,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        backgroundColor: viewModel.canPublish
                            ? Theme.of(context)
                                .colorScheme
                                .primary
                                .withOpacity(0.1)
                            : Colors.grey.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Text(
                        '发布',
                        style: TextStyle(
                          color: viewModel.canPublish
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              body: FadeTransition(
                opacity: _animation,
                child: Container(
                  color: Colors.grey.shade50,
                  child: Column(
                    children: [
                      // Main content in a scrollable area
                      Expanded(
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          physics: const BouncingScrollPhysics(),
                          padding: const EdgeInsets.all(0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Content TextField Card
                              Container(
                                color: Colors.white,
                                padding:
                                    const EdgeInsets.fromLTRB(16, 16, 16, 8),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 动态类型选择 - 移到内容输入前面
                                    MomentTypeSelector(
                                      selectedType: viewModel.momentType,
                                      onTypeSelected: viewModel.setMomentType,
                                    ),

                                    const SizedBox(height: 20),

                                    if (viewModel.momentType.isNotEmpty) ...[
                                      TextField(
                                        controller: _contentController,
                                        focusNode: _contentFocusNode,
                                        maxLines: null,
                                        minLines: 6,
                                        maxLength: PublishMomentConstants
                                            .maxContentLength,
                                        textInputAction:
                                            TextInputAction.newline,
                                        keyboardType: TextInputType.multiline,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          height: 1.5,
                                        ),
                                        decoration: const InputDecoration(
                                          hintText:
                                              '分享你的钓鱼故事、钓获成果、钓场环境、使用的饵料等...',
                                          border: InputBorder.none,
                                          focusedBorder: InputBorder.none,
                                          enabledBorder: InputBorder.none,
                                          errorBorder: InputBorder.none,
                                          disabledBorder: InputBorder.none,
                                          counterText: '',
                                        ),
                                      ),

                                      // Character counter
                                      Align(
                                        alignment: Alignment.centerRight,
                                        child: Text(
                                          '${_contentController.text.length}/${PublishMomentConstants.maxContentLength}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade500,
                                          ),
                                        ),
                                      ),
                                    ],
                                    const SizedBox(height: 4),
                                  ],
                                ),
                              ),

                              // 只有选择了动态类型后才显示其他区域
                              if (viewModel.momentType.isNotEmpty) ...[
                                const SizedBox(height: 8),

                                // Images section
                                Container(
                                  color: Colors.white,
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Section header with image count
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.photo_library_outlined,
                                            size: 20,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            '照片',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          const Spacer(),
                                          Text(
                                            '${viewModel.images.length}/${PublishMomentConstants.maxImages}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),

                                      // Image grid
                                      if (viewModel.images.isEmpty)
                                        _buildAddImageButton()
                                      else
                                        _buildImageGrid(viewModel),

                                      // Add more images button (if we have images but less than max)
                                      if (viewModel.images.isNotEmpty &&
                                          viewModel.images.length <
                                              PublishMomentConstants.maxImages)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 12),
                                          child: Center(
                                            child: OutlinedButton.icon(
                                              onPressed: _pickImages,
                                              icon: const Icon(
                                                  Icons
                                                      .add_photo_alternate_outlined,
                                                  size: 18),
                                              label: const Text('继续添加照片'),
                                              style: OutlinedButton.styleFrom(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 20,
                                                  vertical: 12,
                                                ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 8),

                                // Type-specific input form
                                Container(
                                  color: Colors.white,
                                  padding: const EdgeInsets.all(16),
                                  child: _buildTypeSpecificForm(viewModel),
                                ),

                                const SizedBox(height: 8),

                                // Location selection
                                Container(
                                  color: Colors.white,
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Section header
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.location_on_outlined,
                                            size: 20,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            '钓点位置',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '(可选)',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),

                                      // Location card
                                      _buildLocationCard(viewModel),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 8),

                                // Visibility selection
                                Container(
                                  color: Colors.white,
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Section header
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.visibility_outlined,
                                            size: 20,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            '可见范围',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),

                                      // Visibility options
                                      ListView.separated(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        // 只显示该类型适用的可见范围选项
                                        itemCount: PublishMomentConstants
                                            .visibilityOptions.entries
                                            .where((entry) => viewModel
                                                .getVisibilityOptionsForType(
                                                    viewModel.momentType)
                                                .contains(entry.key))
                                            .length,
                                        separatorBuilder: (context, index) =>
                                            const SizedBox(height: 10),
                                        itemBuilder: (context, index) {
                                          // 过滤出该类型适用的可见范围选项
                                          final filteredEntries =
                                              PublishMomentConstants
                                                  .visibilityOptions.entries
                                                  .where((entry) => viewModel
                                                      .getVisibilityOptionsForType(
                                                          viewModel.momentType)
                                                      .contains(entry.key))
                                                  .toList();

                                          final entry = filteredEntries[index];
                                          final value = entry.key;
                                          final label = entry.value;
                                          final isSelected =
                                              value == viewModel.visibility;
                                          final IconData icon =
                                              PublishMomentConstants
                                                      .visibilityIcons[value] ??
                                                  Icons.visibility;
                                          final String description =
                                              _getVisibilityDescription(value);

                                          return InkWell(
                                            onTap: () =>
                                                viewModel.setVisibility(value),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            child: Container(
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? _getVisibilityColor(value)
                                                        .withOpacity(0.1)
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                border: Border.all(
                                                  color: isSelected
                                                      ? _getVisibilityColor(
                                                          value)
                                                      : Colors.grey.shade300,
                                                  width: isSelected ? 1.5 : 1,
                                                ),
                                              ),
                                              child: Row(
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(8),
                                                    decoration: BoxDecoration(
                                                      color: isSelected
                                                          ? _getVisibilityColor(
                                                                  value)
                                                              .withOpacity(0.2)
                                                          : Colors
                                                              .grey.shade100,
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Icon(
                                                      icon,
                                                      color: isSelected
                                                          ? _getVisibilityColor(
                                                              value)
                                                          : Colors
                                                              .grey.shade600,
                                                      size: 20,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          label,
                                                          style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            fontSize: 15,
                                                            color: isSelected
                                                                ? _getVisibilityColor(
                                                                    value)
                                                                : Colors.grey
                                                                    .shade800,
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 4),
                                                        Text(
                                                          description,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            color: Colors
                                                                .grey.shade600,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (isSelected)
                                                    Icon(
                                                      Icons.check_circle,
                                                      color:
                                                          _getVisibilityColor(
                                                              value),
                                                      size: 20,
                                                    ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],

                              // 如果没有选择类型，显示提示
                              if (viewModel.momentType.isEmpty)
                                Padding(
                                  padding: const EdgeInsets.all(24.0),
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(16),
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.amber.withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.touch_app,
                                            size: 48,
                                            color: Colors.amber.shade700,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        const Text(
                                          '请选择一种动态类型',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '不同类型的动态有不同的内容展示形式',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey.shade600,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 32),

                                        // Type descriptions
                                        _buildTypeDescription(
                                          icon: Icons.set_meal,
                                          title: '钓获分享',
                                          description: '展示您的钓鱼成果，分享鱼种、重量、钓法等详情',
                                          color: Colors.green,
                                        ),
                                        const SizedBox(height: 16),
                                        _buildTypeDescription(
                                          icon: Icons.backpack,
                                          title: '装备展示',
                                          description: '展示您的钓鱼装备，分享品牌、型号、使用体验等',
                                          color: Colors.blue,
                                        ),
                                        const SizedBox(height: 16),
                                        _buildTypeDescription(
                                          icon: Icons.tips_and_updates,
                                          title: '技巧分享',
                                          description: '分享您的钓鱼技巧、经验和方法',
                                          color: Colors.orange,
                                        ),
                                        const SizedBox(height: 16),
                                        _buildTypeDescription(
                                          icon: Icons.help_outline,
                                          title: '问答求助',
                                          description: '提出钓鱼相关问题，寻求社区帮助和建议',
                                          color: Colors.purple,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                              // Bottom padding to ensure content is visible above keyboard
                              const SizedBox(height: 100),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Floating publish button for convenience
              floatingActionButton: AnimatedOpacity(
                opacity: viewModel.canPublish &&
                        !_isPublishing &&
                        viewModel.momentType.isNotEmpty
                    ? 1.0
                    : 0.0,
                duration: const Duration(milliseconds: 200),
                child: FloatingActionButton.extended(
                  onPressed:
                      viewModel.canPublish && !_isPublishing ? _publish : null,
                  icon: const Icon(Icons.send),
                  label: const Text('发布'),
                  elevation: 2,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAddImageButton() {
    final screenWidth = MediaQuery.of(context).size.width;
    // 计算合适的高度，保持宽高比例为4:3
    final containerHeight = (screenWidth - 32) * 0.75;

    return InkWell(
      onTap: _pickImages,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: containerHeight,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add_photo_alternate,
                size: 40,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '添加照片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.grey.shade200,
                ),
              ),
              child: Text(
                '最多${PublishMomentConstants.maxImages}张',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid(PublishMomentViewModel viewModel) {
    final int crossAxisCount = viewModel.images.length > 3 ? 3 : 2;
    final List<String> imageIds = List.generate(
        viewModel.images.length,
        (index) =>
            viewModel.images[index].url ?? viewModel.images[index].file.path);

    return Column(
      children: [
        // 拖动提示
        if (viewModel.images.length > 1)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.drag_indicator,
                    size: 18, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  "长按并拖动可调整图片顺序",
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

        // 可拖拽排序的网格
        ReorderableBuilder(
          enableDraggable: !viewModel.images.any((img) => img.isUploading),
          dragChildBoxDecoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          onReorder: (ReorderedListFunction reorderedListFunction) {
            setState(() {
              var list = reorderedListFunction(viewModel.images)
                  as List<UploadedImage>;
              viewModel.reorderImages(list);
            });
          },
          builder: (children) {
            return GridView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              children: children,
            );
          },
          children: List.generate(
            viewModel.images.length,
            (index) {
              final image = viewModel.images[index];

              return Container(
                key: ValueKey(imageIds[index]),
                child: Stack(
                  children: [
                    // 图片容器
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        width: double.infinity,
                        height: double.infinity,
                        child: image.url != null
                            ? Image.network(
                                image.url!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.broken_image,
                                      color: Colors.grey.shade400,
                                      size: 36,
                                    ),
                                  );
                                },
                              )
                            : FutureBuilder(
                                future: image.file.readAsBytes(),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState ==
                                          ConnectionState.done &&
                                      snapshot.hasData) {
                                    return Image.memory(
                                      snapshot.data!,
                                      fit: BoxFit.cover,
                                    );
                                  }
                                  return Center(
                                    child: CircularProgressIndicator(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  );
                                },
                              ),
                      ),
                    ),

                    // 拖拽提示 (当不在上传状态时显示)
                    if (!image.isUploading && !image.hasError)
                      Positioned(
                        bottom: 6,
                        right: 6,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.6),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.drag_handle,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),

                    // 上传中指示器
                    if (image.isUploading)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 3,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '上传中',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.5),
                                        blurRadius: 2,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // 错误指示器
                    if (image.hasError)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: Colors.white,
                                  size: 28,
                                ),
                                const SizedBox(height: 8),
                                ElevatedButton.icon(
                                  onPressed: () => {
                                    viewModel.retryUpload(index),
                                  },
                                  icon: const Icon(Icons.refresh, size: 14),
                                  label: const Text('重试'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: Colors.red,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    textStyle: const TextStyle(fontSize: 12),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // 删除按钮
                    Positioned(
                      top: 6,
                      right: 6,
                      child: GestureDetector(
                        onTap: () => viewModel.removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.6),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),

                    // 图片编号指示器
                    Positioned(
                      top: 6,
                      left: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationCard(PublishMomentViewModel viewModel) {
    if (viewModel.fishingSpot == null) {
      return Card(
        elevation: 0,
        color: Colors.grey.shade50,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        child: InkWell(
          onTap: _selectLocation,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.add_location_alt_outlined,
                  size: 24,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    '选择钓点位置',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey.shade400,
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      return Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.location_on,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          viewModel.fishingSpot!.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          viewModel.fishingSpot!.address,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _selectLocation,
                      icon: const Icon(Icons.edit_location_alt, size: 16),
                      label: const Text('更换位置'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => viewModel.setFishingSpot(null),
                      icon: const Icon(Icons.delete_outline, size: 16),
                      label: const Text('移除位置'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }
  }

  Color _getVisibilityColor(String value) {
    switch (value) {
      case 'public':
        return Colors.green;
      case 'followers':
        return Colors.blue;
      case 'private':
        return Colors.grey;
      default:
        return Colors.green;
    }
  }

  String _getVisibilityDescription(String value) {
    // 基础描述
    Map<String, String> baseDescriptions = {
      'public': '所有人都可以看到您的这条动态',
      'followers': '只有关注您的人才能看到这条动态',
      'private': '只有您自己可以看到这条动态',
    };

    // 根据不同动态类型补充附加描述
    String additionalInfo = '';
    if (_viewModel.momentType == '钓获分享') {
      if (value == 'public') {
        additionalInfo = '，让更多钓友欣赏您的成果';
      } else if (value == 'private') {
        additionalInfo = '，适合保存个人记录';
      }
    } else if (_viewModel.momentType == '装备展示') {
      if (value == 'public') {
        additionalInfo = '，分享您的装备给所有钓友';
      }
    } else if (_viewModel.momentType == '技巧分享') {
      if (value == 'public') {
        additionalInfo = '，帮助更多钓友提升技能';
      }
    } else if (_viewModel.momentType == '问答求助') {
      if (value == 'public') {
        additionalInfo = '，获得更多回答和帮助';
      }
    }

    return baseDescriptions[value]! + additionalInfo;
  }

  Widget _buildTypeSpecificForm(PublishMomentViewModel viewModel) {
    final fishingSpotViewModel = getIt<FishingSpotViewModel>();
    fishingSpotViewModel.loadFishTypes();

    switch (viewModel.momentType) {
      case '钓获分享':
        return EnhancedFishingCatchForm(
          onDataChanged: viewModel.setTypeSpecificData,
          initialData: viewModel.typeSpecificData,
          fishingSpotViewModel: fishingSpotViewModel,
        );
      case '装备展示':
        return EquipmentForm(
          onDataChanged: viewModel.setTypeSpecificData,
          initialData: viewModel.typeSpecificData,
          fishingSpotViewModel: fishingSpotViewModel,
        );
      case '技巧分享':
        return TechniqueForm(
          onDataChanged: viewModel.setTypeSpecificData,
          initialData: viewModel.typeSpecificData,
          fishingSpotViewModel: fishingSpotViewModel,
        );
      case '问答求助':
        return QuestionForm(
          onDataChanged: viewModel.setTypeSpecificData,
          initialData: viewModel.typeSpecificData,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildTypeDescription({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 24,
            color: color,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
