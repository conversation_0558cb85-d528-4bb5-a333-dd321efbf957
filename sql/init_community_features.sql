-- 社区功能相关表初始化脚本
-- 执行前请确保数据库已存在

USE fishing_app;

-- 1. 收藏表
DROP TABLE IF EXISTS `bookmarks`;
CREATE TABLE `bookmarks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `target_type` varchar(20) NOT NULL COMMENT '收藏目标类型：moment-动态, spot-钓点',
  `target_id` bigint NOT NULL COMMENT '收藏目标ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏表';

-- 2. 举报表
DROP TABLE IF EXISTS `reports`;
CREATE TABLE `reports` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reporter_id` bigint NOT NULL COMMENT '举报人ID',
  `target_type` varchar(20) NOT NULL COMMENT '举报目标类型：moment-动态, user-用户',
  `target_id` bigint NOT NULL COMMENT '举报目标ID',
  `reason` varchar(50) NOT NULL COMMENT '举报原因',
  `description` text COMMENT '详细描述',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '处理状态：0-待处理, 1-已处理, 2-已忽略',
  `handler_id` bigint COMMENT '处理人ID',
  `handle_time` datetime COMMENT '处理时间',
  `handle_result` text COMMENT '处理结果',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_reporter_id` (`reporter_id`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='举报表';

-- 3. 通知表
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '接收用户ID',
  `type` varchar(20) NOT NULL COMMENT '通知类型：like-点赞, comment-评论, follow-关注, system-系统',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` varchar(500) NOT NULL COMMENT '通知内容',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读, 1-已读',
  `related_type` varchar(20) COMMENT '关联类型：moment-动态, user-用户, system-系统',
  `related_id` bigint COMMENT '关联ID',
  `image_url` varchar(500) COMMENT '图片URL',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_related` (`related_type`, `related_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 4. 插入测试数据（可选）
-- 插入一些示例通知
INSERT INTO `notifications` (`user_id`, `type`, `title`, `content`, `is_read`, `related_type`, `related_id`) VALUES
(1, 'system', '欢迎使用', '欢迎来到钓鱼社区！', 0, 'system', NULL),
(1, 'like', '收到点赞', '您的动态收到了新的点赞', 0, 'moment', 1),
(2, 'comment', '收到评论', '您的动态收到了新的评论', 0, 'moment', 1),
(2, 'follow', '新的关注者', '有用户关注了您', 0, 'user', 1);

-- 5. 创建索引优化查询性能
-- 收藏表索引
CREATE INDEX `idx_bookmarks_user_created` ON `bookmarks` (`user_id`, `created_at` DESC);
CREATE INDEX `idx_bookmarks_target_created` ON `bookmarks` (`target_type`, `target_id`, `created_at` DESC);

-- 举报表索引
CREATE INDEX `idx_reports_status_created` ON `reports` (`status`, `created_at` DESC);
CREATE INDEX `idx_reports_target_status` ON `reports` (`target_type`, `target_id`, `status`);

-- 通知表索引
CREATE INDEX `idx_notifications_user_read_created` ON `notifications` (`user_id`, `is_read`, `created_at` DESC);
CREATE INDEX `idx_notifications_type_created` ON `notifications` (`type`, `created_at` DESC);

-- 6. 创建视图方便查询（可选）
-- 用户收藏统计视图
CREATE OR REPLACE VIEW `user_bookmark_stats` AS
SELECT 
    user_id,
    target_type,
    COUNT(*) as bookmark_count,
    MAX(created_at) as last_bookmark_time
FROM bookmarks 
GROUP BY user_id, target_type;

-- 用户通知统计视图
CREATE OR REPLACE VIEW `user_notification_stats` AS
SELECT 
    user_id,
    COUNT(*) as total_notifications,
    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,
    MAX(created_at) as last_notification_time
FROM notifications 
GROUP BY user_id;

COMMIT;
